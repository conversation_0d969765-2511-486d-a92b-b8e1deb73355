.PHONY: all build test bench run clean

all: build test

build:
	@echo "Building Rust components..."
	cargo build --release
	@echo "Building Python packages..."
	python -m build

test:
	@echo "Running Rust tests..."
	cargo test
	@echo "Running Python tests..."
	pytest -v

bench:
	@echo "Running latency benchmarks..."
	cargo bench
	python scripts/benchmark_latency.py

run:
	@echo "Starting development stack..."
	tilt up

clean:
	cargo clean
	rm -rf build/ dist/ *.egg-info
	find . -type d -name __pycache__ -exec rm -rf {} +

proto:
	@echo "Generating protobuf code..."
	protoc --python_out=. --pyi_out=. proto/*.proto
	cargo build --features proto