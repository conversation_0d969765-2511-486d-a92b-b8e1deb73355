# Tiltfile for BTC-USD Scalping Stack
# Orchestrates local development environment

# Load extensions
load('ext://restart_process', 'docker_build_with_restart')

# Configuration
k8s_yaml('deploy/k8s/namespace.yaml')
k8s_yaml('deploy/k8s/redpanda.yaml')
k8s_yaml('deploy/k8s/prometheus.yaml')

# Build Rust services with caching
docker_build(
    'btc-scalp/ingest-rs',
    context='.',
    dockerfile_contents='''
FROM rust:1.74 as builder
WORKDIR /app
COPY Cargo.toml Cargo.lock ./
COPY ingest-rs ./ingest-rs
RUN cargo build --release -p ingest-rs

FROM debian:bookworm-slim
RUN apt-get update && apt-get install -y ca-certificates && rm -rf /var/lib/apt/lists/*
COPY --from=builder /app/target/release/ingest /usr/local/bin/
CMD ["ingest"]
''',
    only=[
        'Cargo.toml',
        'Cargo.lock',
        'ingest-rs/',
    ],
)

# Kubernetes resources
k8s_resource(
    'redpanda',
    port_forwards='9092:9092',
    labels=['infra'],
)

# Local commands
local_resource(
    'tests',
    cmd='make test',
    deps=['featurelib/', 'modellib/', 'ingest-rs/src/', 'risk-rs/src/'],
    labels=['testing'],
)