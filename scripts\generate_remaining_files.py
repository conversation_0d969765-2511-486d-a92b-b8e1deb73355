#!/usr/bin/env python3
"""
Script to generate all remaining project files.
Run this after the initial setup to create all remaining files.
"""

import os
from pathlib import Path

# Get the project root
PROJECT_ROOT = Path(__file__).parent.parent

# Define all remaining files with their content
FILES = {
    # Python init files
    "modellib/__init__.py": '''"""
Machine learning models for BTC-USD prediction.
"""

from .temporal_cnn import TemporalCNN, ModelConfig, Prediction, ModelTrainer

__version__ = "0.1.0"
__all__ = ["TemporalCNN", "ModelConfig", "Prediction", "ModelTrainer"]''',

    "backtest/__init__.py": '''"""
Backtesting framework for BTC scalping strategies.
"""

from .backtest import BacktestEngine, BacktestResult, Trade, plot_backtest_results

__version__ = "0.1.0"
__all__ = ["BacktestEngine", "BacktestResult", "Trade", "plot_backtest_results"]''',

    "config/__init__.py": '''"""
Configuration management for BTC scalping system.
"""

from .validator import (
    Config<PERSON>oader,
    FullConfig,
    VenueConfig,
    StrategyConfig,
    FeatureConfig,
    ModelConfig,
    KafkaConfig,
    MonitoringConfig,
)

__version__ = "0.1.0"
__all__ = [
    "ConfigLoader",
    "FullConfig",
    "VenueConfig",
    "StrategyConfig",
    "FeatureConfig",
    "ModelConfig",
    "KafkaConfig",
    "MonitoringConfig",
]''',

    "tests/__init__.py": '''"""
Test suite for BTC scalping system.
"""

# This file makes the tests directory a Python package''',

    # Pre-commit config
    ".pre-commit-config.yaml": '''repos:
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.4.0
    hooks:
      - id: trailing-whitespace
      - id: end-of-file-fixer
      - id: check-yaml
      - id: check-added-large-files
  
  - repo: https://github.com/psf/black
    rev: 23.10.0
    hooks:
      - id: black
  
  - repo: https://github.com/charliermarsh/ruff-pre-commit
    rev: v0.1.0
    hooks:
      - id: ruff''',

    # Tiltfile
    "Tiltfile": '''# Tiltfile for BTC-USD Scalping Stack
# Orchestrates local development environment

# Load extensions
load('ext://restart_process', 'docker_build_with_restart')

# Configuration
k8s_yaml('deploy/k8s/namespace.yaml')
k8s_yaml('deploy/k8s/redpanda.yaml')
k8s_yaml('deploy/k8s/prometheus.yaml')

# Build Rust services with caching
docker_build(
    'btc-scalp/ingest-rs',
    context='.',
    dockerfile_contents=\'\'\'
FROM rust:1.74 as builder
WORKDIR /app
COPY Cargo.toml Cargo.lock ./
COPY ingest-rs ./ingest-rs
RUN cargo build --release -p ingest-rs

FROM debian:bookworm-slim
RUN apt-get update && apt-get install -y ca-certificates && rm -rf /var/lib/apt/lists/*
COPY --from=builder /app/target/release/ingest /usr/local/bin/
CMD ["ingest"]
\'\'\',
    only=[
        'Cargo.toml',
        'Cargo.lock',
        'ingest-rs/',
    ],
)

# Kubernetes resources
k8s_resource(
    'redpanda',
    port_forwards='9092:9092',
    labels=['infra'],
)

# Local commands
local_resource(
    'tests',
    cmd='make test',
    deps=['featurelib/', 'modellib/', 'ingest-rs/src/', 'risk-rs/src/'],
    labels=['testing'],
)''',

    # Docker Compose files
    "docker-compose.test.yml": '''version: '3.8'

services:
  # Redpanda (Kafka compatible)
  redpanda:
    image: redpandadata/redpanda:v23.2.15
    container_name: redpanda-test
    command:
      - redpanda
      - start
      - --kafka-addr internal://0.0.0.0:9092,external://0.0.0.0:19092
      - --advertise-kafka-addr internal://redpanda:9092,external://localhost:19092
      - --smp 2
      - --memory 1G
      - --overprovisioned
    ports:
      - "19092:19092"
      - "9644:9644"
    healthcheck:
      test: ["CMD-SHELL", "rpk cluster health | grep 'Healthy'"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - btc-scalp-test

  # Mock Binance WebSocket
  mock-binance:
    build:
      context: .
      dockerfile: tests/mock/Dockerfile.mock-binance
    container_name: mock-binance-test
    ports:
      - "8765:8765"
    environment:
      - TICK_RATE=100
      - PRICE_VOLATILITY=0.001
    networks:
      - btc-scalp-test

networks:
  btc-scalp-test:
    driver: bridge''',

    # SETUP.md
    "SETUP.md": '''# BTC Scalping System - Setup Instructions

## Prerequisites

Before starting, ensure you have the following installed:

- **Python 3.11+**
- **Rust 1.74+** (with cargo)
- **Docker & Docker Compose**
- **kubectl** (for Kubernetes deployment)
- **Git**

## Quick Start

### 1. Clone and Setup

```bash
# Clone the repository (when available on GitHub)
git clone https://github.com/your-org/btc-scalp.git
cd btc-scalp

# Run the bootstrap script
chmod +x scripts/bootstrap_dev.sh
./scripts/bootstrap_dev.sh
```

### 2. Configure Environment

```bash
# Copy environment template
cp .env.example .env

# Edit .env with your Binance credentials
# IMPORTANT: Never commit .env to version control!
nano .env
```

### 3. Verify Setup

```bash
# Run verification script
python scripts/verify_setup.py
```

### 4. Start Development Environment

```bash
# Using Tilt (recommended)
tilt up

# Or using Docker Compose
docker-compose -f docker-compose.test.yml up -d
```

## Development Workflow

See README.md for detailed development instructions.''',
}

# Rust files
RUST_FILES = {
    # Ingest Cargo.toml
    "ingest-rs/Cargo.toml": '''[package]
name = "ingest-rs"
version = "0.1.0"
edition = "2021"

[dependencies]
tokio = { version = "1.34", features = ["full"] }
tokio-tungstenite = { version = "0.21", features = ["native-tls"] }
futures-util = "0.3"
rdkafka = { version = "0.35", features = ["cmake-build", "ssl", "gssapi"] }
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
anyhow = "1.0"
tracing = "0.1"
tracing-subscriber = { version = "0.3", features = ["env-filter"] }
prost = "0.12"
crossbeam = "0.8"

[build-dependencies]
prost-build = "0.12"

[[bin]]
name = "ingest"
path = "src/main.rs"

[profile.release]
lto = true
codegen-units = 1
opt-level = 3
debug = false''',

    # Risk Cargo.toml
    "risk-rs/Cargo.toml": '''[package]
name = "risk-rs"
version = "0.1.0"
edition = "2021"

[dependencies]
anyhow = "1.0"
rust_decimal = "1.33"
rust_decimal_macros = "1.33"
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
tracing = "0.1"
tracing-subscriber = { version = "0.3", features = ["env-filter"] }
prost = "0.12"
tonic = "0.10"
uuid = { version = "1.6", features = ["v4", "serde"] }
tokio = { version = "1.34", features = ["full"] }
crossbeam = "0.8"

[build-dependencies]
prost-build = "0.12"
tonic-build = "0.10"

[[bin]]
name = "risk-engine"
path = "src/main.rs"

[lib]
name = "risk"
path = "src/lib.rs"

[profile.release]
lto = true
codegen-units = 1
opt-level = 3''',

    # Risk build.rs
    "risk-rs/build.rs": '''use std::env;
use std::path::PathBuf;

fn main() -> Result<(), Box<dyn std::error::Error>> {
    let out_dir = PathBuf::from(env::var("OUT_DIR").unwrap());
    
    tonic_build::configure()
        .build_server(true)
        .build_client(true)
        .out_dir(&out_dir)
        .compile(
            &["../proto/strategy.proto"],
            &["../proto"],
        )?;
    
    println!("cargo:rerun-if-changed=../proto/strategy.proto");
    
    Ok(())
}''',
}

def create_files():
    """Create all remaining files."""
    print("Creating remaining project files...")
    
    # Create Python files
    for filepath, content in FILES.items():
        full_path = PROJECT_ROOT / filepath
        full_path.parent.mkdir(parents=True, exist_ok=True)
        
        with open(full_path, 'w', encoding='utf-8') as f:
            f.write(content)
        print(f"Created: {filepath}")
    
    # Create Rust files
    for filepath, content in RUST_FILES.items():
        full_path = PROJECT_ROOT / filepath
        full_path.parent.mkdir(parents=True, exist_ok=True)
        
        with open(full_path, 'w', encoding='utf-8') as f:
            f.write(content)
        print(f"Created: {filepath}")
    
    print("\n✅ All remaining files created successfully!")
    print("\nNext steps:")
    print("1. Review the created files")
    print("2. Run: ./scripts/bootstrap_dev.sh")
    print("3. Configure your .env file")
    print("4. Start development with: tilt up")

if __name__ == "__main__":
    create_files()
