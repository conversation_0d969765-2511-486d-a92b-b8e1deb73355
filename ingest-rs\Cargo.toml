[package]
name = "ingest-rs"
version = "0.1.0"
edition = "2021"

[dependencies]
tokio = { version = "1.34", features = ["full"] }
tokio-tungstenite = { version = "0.21", features = ["native-tls"] }
futures-util = "0.3"
rdkafka = { version = "0.35", features = ["cmake-build", "ssl", "gssapi"] }
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
anyhow = "1.0"
tracing = "0.1"
tracing-subscriber = { version = "0.3", features = ["env-filter"] }
prost = "0.12"
crossbeam = "0.8"

[build-dependencies]
prost-build = "0.12"

[[bin]]
name = "ingest"
path = "src/main.rs"

[profile.release]
lto = true
codegen-units = 1
opt-level = 3
debug = false