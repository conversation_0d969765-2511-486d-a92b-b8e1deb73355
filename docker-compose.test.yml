version: '3.8'

services:
  # Redpanda (Kafka compatible)
  redpanda:
    image: redpandadata/redpanda:v23.2.15
    container_name: redpanda-test
    command:
      - redpanda
      - start
      - --kafka-addr internal://0.0.0.0:9092,external://0.0.0.0:19092
      - --advertise-kafka-addr internal://redpanda:9092,external://localhost:19092
      - --smp 2
      - --memory 1G
      - --overprovisioned
    ports:
      - "19092:19092"
      - "9644:9644"
    healthcheck:
      test: ["CMD-SHELL", "rpk cluster health | grep 'Healthy'"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - btc-scalp-test

  # Mock Binance WebSocket
  mock-binance:
    build:
      context: .
      dockerfile: tests/mock/Dockerfile.mock-binance
    container_name: mock-binance-test
    ports:
      - "8765:8765"
    environment:
      - TICK_RATE=100
      - PRICE_VOLATILITY=0.001
    networks:
      - btc-scalp-test

networks:
  btc-scalp-test:
    driver: bridge
