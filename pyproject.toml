[build-system]
requires = ["setuptools>=65", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "btc-scalp"
version = "0.1.0"
description = "Production-grade BTC-USD scalping system"
readme = "README.md"
requires-python = ">=3.11"
authors = [
    {name = "BTC Scalp Team", email = "<EMAIL>"}
]
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Financial and Insurance Industry",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Rust",
    "Topic :: Office/Business :: Financial :: Investment",
    "License :: Proprietary",
]

dependencies = [
    "polars>=0.20.0",
    "numpy>=1.24.0,<2.0",
    "pandas>=2.0.0",
    "pydantic>=2.5.0",
    "pydantic-settings>=2.1.0",
    "torch>=2.1.0",
    "scikit-learn>=1.3.0",
    "safetensors>=0.4.0",
    "mlflow>=2.8.0",
    "grpcio>=1.59.0",
    "grpcio-tools>=1.59.0",
    "protobuf>=4.24.0",
    "confluent-kafka>=2.3.0",
    "redis>=5.0.0",
    "prometheus-client>=0.18.0",
    "aiohttp>=3.9.0",
    "websockets>=12.0",
    "toml>=0.10.2",
    "joblib>=1.3.0",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.4.0",
    "pytest-asyncio>=0.21.0",
    "pytest-cov>=4.1.0",
    "hypothesis>=6.90.0",
    "black>=23.10.0",
    "ruff>=0.1.0",
    "mypy>=1.6.0",
    "pre-commit>=3.5.0",
    "ipython>=8.17.0",
]

monitoring = [
    "matplotlib>=3.8.0",
    "seaborn>=0.13.0",
    "plotly>=5.18.0",
]

[project.scripts]
btc-feature-engine = "featurelib.service:main"
btc-backtest = "backtest.backtest:main"
btc-dashboard = "scripts.dashboard:main"
btc-benchmark = "scripts.benchmark_latency:main"

[tool.setuptools.packages.find]
where = ["."]
include = ["featurelib*", "modellib*", "backtest*", "config*"]

[tool.black]
line-length = 100
target-version = ['py311']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.mypy_cache
  | \.venv
  | build
  | dist
  | proto
)/
'''

[tool.ruff]
line-length = 100
target-version = "py311"
select = [
    "E",    # pycodestyle errors
    "W",    # pycodestyle warnings
    "F",    # pyflakes
    "I",    # isort
    "N",    # pep8-naming
    "UP",   # pyupgrade
    "B",    # flake8-bugbear
    "C4",   # flake8-comprehensions
    "DTZ",  # flake8-datetimez
    "S",    # flake8-bandit
    "SIM",  # flake8-simplify
    "RUF",  # Ruff-specific rules
]
ignore = [
    "E501",  # line too long (handled by black)
    "B008",  # do not perform function calls in argument defaults
    "S101",  # use of assert detected
]
exclude = [
    ".git",
    "__pycache__",
    "build",
    "dist",
    ".venv",
    "proto",
]

[tool.ruff.per-file-ignores]
"tests/*" = ["S101"]  # Allow assert in tests

[tool.mypy]
python_version = "3.11"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
ignore_missing_imports = true
exclude = [
    "proto/",
    "build/",
    "dist/",
]

[tool.pytest.ini_options]
minversion = "7.0"
addopts = """
    -ra
    --strict-markers
    --cov=featurelib
    --cov=modellib
    --cov=backtest
    --cov-branch
    --cov-report=term-missing
    --cov-report=html
    --cov-report=xml
"""
testpaths = ["tests"]
python_files = ["test_*.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
asyncio_mode = "auto"

[tool.coverage.run]
branch = true
source = ["featurelib", "modellib", "backtest"]
omit = [
    "*/tests/*",
    "*/proto/*",
    "*/__init__.py",
]

[tool.coverage.report]
precision = 2
show_missing = true
skip_covered = false
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "raise AssertionError",
    "raise NotImplementedError",
    "if __name__ == .__main__.:",
    "if TYPE_CHECKING:",
]