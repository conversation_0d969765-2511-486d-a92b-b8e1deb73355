[venue.binance]
ws_url = "wss://stream.binance.com:9443/ws"
api_key = {env = "BINANCE_API_KEY"}
api_secret = {env = "BINANCE_SECRET"}

[strategy.scalp]
lookback_seconds = 300
prediction_horizon = 300
kelly_cap = 0.10
max_leverage = 2.0
max_dd_pct = 1.0
target_vol_5m = 0.005

[features]
window_ms = [5_000, 30_000, 60_000, 300_000]
book_levels = 10
trade_imbalance_depth = 50
sentiment_zscore_half_life = 30

[ml.model]
type = "TemporalCNN"
quantization_bits = 8
calibration_method = "isotonic"

[infrastructure.kafka]
bootstrap_servers = "localhost:9092"
topics = ["ticks.raw", "features.micro", "signals", "orders"]

[monitoring]
prometheus_port = 9090
latency_threshold_ms = 5