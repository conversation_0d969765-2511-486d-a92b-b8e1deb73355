"""
Feature engineering library for BTC-USD scalping.
All computations are vectorized using Polars for maximum performance.
"""

import polars as pl
import numpy as np
from typing import List, Dict, Tuple, Optional
from dataclasses import dataclass
from numba import njit, prange
import logging

logger = logging.getLogger(__name__)


@dataclass
class MicroConfig:
    """Configuration for micro features."""
    window_ms: List[int]
    book_levels: int
    trade_imbalance_depth: int
    sentiment_zscore_half_life: float


@dataclass
class MicroFeatureVector:
    """Computed feature vector."""
    timestamp_ns: int
    values: np.ndarray
    feature_names: List[str]
    
    def to_dict(self) -> Dict[str, float]:
        """Convert to dictionary for debugging."""
        return dict(zip(self.feature_names, self.values))


class FeatureComputer:
    """Main feature computation class."""
    
    def __init__(self, config: MicroConfig):
        self.config = config
        self.feature_names = self._build_feature_names()
    
    def _build_feature_names(self) -> List[str]:
        """Build list of feature names for interpretability."""
        names = []
        
        # Book features
        for level in range(self.config.book_levels):
            names.extend([
                f"bid_price_{level}",
                f"bid_size_{level}",
                f"ask_price_{level}",
                f"ask_size_{level}",
            ])
        
        # Imbalance features
        for window in self.config.window_ms:
            names.extend([
                f"book_imbalance_{window}ms",
                f"trade_imbalance_{window}ms",
                f"vwap_{window}ms",
                f"volatility_{window}ms",
                f"volume_{window}ms",
            ])
        
        # Microstructure features
        names.extend([
            "spread_bps",
            "mid_price",
            "weighted_mid_price",
            "micro_price",
            "book_depth_imbalance",
            "effective_spread",
        ])
        
        return names
    
    def compute_features(
        self,
        book: pl.DataFrame,
        trades: pl.DataFrame,
        current_time_ns: int,
    ) -> MicroFeatureVector:
        """
        Compute all features from book and trade data.
        
        Args:
            book: DataFrame with columns [timestamp_ns, side, level, price, size]
            trades: DataFrame with columns [timestamp_ns, price, size, is_buy]
            current_time_ns: Current timestamp in nanoseconds
        
        Returns:
            MicroFeatureVector with computed features
        """
        features = []
        
        # Book features (latest snapshot)
        book_features = self._compute_book_features(book, current_time_ns)
        features.extend(book_features)
        
        # Rolling window features
        for window_ms in self.config.window_ms:
            window_ns = window_ms * 1_000_000
            window_start = current_time_ns - window_ns
            
            # Filter data for window
            window_book = book.filter(pl.col("timestamp_ns") >= window_start)
            window_trades = trades.filter(pl.col("timestamp_ns") >= window_start)
            
            # Compute windowed features
            imbalance = self._compute_book_imbalance(window_book)
            trade_imb = self._compute_trade_imbalance(window_trades)
            vwap = self._compute_vwap(window_trades)
            vol = self._compute_volatility(window_trades)
            volume = self._compute_volume(window_trades)
            
            features.extend([imbalance, trade_imb, vwap, vol, volume])
        
        # Microstructure features
        micro_features = self._compute_microstructure_features(book, trades, current_time_ns)
        features.extend(micro_features)
        
        return MicroFeatureVector(
            timestamp_ns=current_time_ns,
            values=np.array(features, dtype=np.float32),
            feature_names=self.feature_names
        )
    
    def _compute_book_features(self, book: pl.DataFrame, current_time_ns: int) -> List[float]:
        """Extract latest book snapshot features."""
        # Get latest book state
        latest_book = (
            book
            .filter(pl.col("timestamp_ns") == book["timestamp_ns"].max())
            .sort(["side", "level"])
        )
        
        features = []
        
        # Extract bid levels
        bids = latest_book.filter(pl.col("side") == "bid")
        for level in range(self.config.book_levels):
            if level < len(bids):
                row = bids[level]
                features.extend([row["price"], row["size"]])
            else:
                features.extend([0.0, 0.0])
        
        # Extract ask levels
        asks = latest_book.filter(pl.col("side") == "ask")
        for level in range(self.config.book_levels):
            if level < len(asks):
                row = asks[level]
                features.extend([row["price"], row["size"]])
            else:
                features.extend([0.0, 0.0])
        
        return features
    
    def _compute_book_imbalance(self, book: pl.DataFrame) -> float:
        """Compute order book imbalance."""
        if book.is_empty():
            return 0.0
        
        # Aggregate by timestamp and side
        imbalances = (
            book
            .group_by(["timestamp_ns", "side"])
            .agg(pl.col("size").sum().alias("total_size"))
            .pivot(values="total_size", index="timestamp_ns", columns="side")
            .fill_null(0.0)
        )
        
        if "bid" not in imbalances.columns or "ask" not in imbalances.columns:
            return 0.0
        
        # Compute imbalance
        imbalances = imbalances.with_columns(
            ((pl.col("bid") - pl.col("ask")) / (pl.col("bid") + pl.col("ask") + 1e-8))
            .alias("imbalance")
        )
        
        # Return time-weighted average
        return imbalances["imbalance"].mean() or 0.0
    
    def _compute_trade_imbalance(self, trades: pl.DataFrame) -> float:
        """Compute trade flow imbalance (VPIN-like)."""
        if trades.is_empty():
            return 0.0
        
        # Aggregate buy/sell volume
        imbalance = (
            trades
            .group_by("is_buy")
            .agg(pl.col("size").sum().alias("volume"))
        )
        
        buy_vol = imbalance.filter(pl.col("is_buy"))["volume"].sum() or 0.0
        sell_vol = imbalance.filter(~pl.col("is_buy"))["volume"].sum() or 0.0
        
        total_vol = buy_vol + sell_vol
        if total_vol < 1e-8:
            return 0.0
        
        return (buy_vol - sell_vol) / total_vol
    
    def _compute_vwap(self, trades: pl.DataFrame) -> float:
        """Compute volume-weighted average price."""
        if trades.is_empty():
            return 0.0
        
        vwap = (
            trades
            .select([
                (pl.col("price") * pl.col("size")).sum(),
                pl.col("size").sum()
            ])
        )
        
        value = vwap[0, 0]
        volume = vwap[0, 1]
        
        return value / volume if volume > 0 else 0.0
    
    def _compute_volatility(self, trades: pl.DataFrame) -> float:
        """Compute realized volatility from trades."""
        if len(trades) < 2:
            return 0.0
        
        # Calculate log returns
        prices = trades.sort("timestamp_ns")["price"]
        if len(prices) < 2:
            return 0.0
        
        log_returns = np.log(prices[1:] / prices[:-1])
        
        # Annualized volatility (assuming 5min bars, 288 per day)
        return np.std(log_returns) * np.sqrt(288 * 365) if len(log_returns) > 0 else 0.0
    
    def _compute_volume(self, trades: pl.DataFrame) -> float:
        """Compute total volume."""
        return trades["size"].sum() if not trades.is_empty() else 0.0
    
    def _compute_microstructure_features(
        self,
        book: pl.DataFrame,
        trades: pl.DataFrame,
        current_time_ns: int
    ) -> List[float]:
        """Compute advanced microstructure features."""
        features = []
        
        # Get latest book
        latest_book = book.filter(pl.col("timestamp_ns") == book["timestamp_ns"].max())
        
        if latest_book.is_empty():
            return [0.0] * 6
        
        # Best bid/ask
        best_bid = (
            latest_book
            .filter(pl.col("side") == "bid")
            .filter(pl.col("level") == 0)
            .select("price")
        )
        
        best_ask = (
            latest_book
            .filter(pl.col("side") == "ask")
            .filter(pl.col("level") == 0)
            .select("price")
        )
        
        if best_bid.is_empty() or best_ask.is_empty():
            return [0.0] * 6
        
        bid_price = best_bid[0, 0]
        ask_price = best_ask[0, 0]
        
        # Spread in basis points
        spread_bps = 10000 * (ask_price - bid_price) / bid_price
        features.append(spread_bps)
        
        # Mid price
        mid_price = (bid_price + ask_price) / 2
        features.append(mid_price)
        
        # Weighted mid price (by size)
        bid_size = (
            latest_book
            .filter(pl.col("side") == "bid")
            .filter(pl.col("level") == 0)
            .select("size")[0, 0]
        )
        
        ask_size = (
            latest_book
            .filter(pl.col("side") == "ask")
            .filter(pl.col("level") == 0)
            .select("size")[0, 0]
        )
        
        weighted_mid = (bid_price * ask_size + ask_price * bid_size) / (bid_size + ask_size)
        features.append(weighted_mid)
        
        # Micro price (using multiple levels)
        micro_price = self._compute_micro_price(latest_book)
        features.append(micro_price)
        
        # Book depth imbalance
        depth_imbalance = self._compute_depth_imbalance(latest_book)
        features.append(depth_imbalance)
        
        # Effective spread from recent trades
        effective_spread = self._compute_effective_spread(trades, mid_price)
        features.append(effective_spread)
        
        return features
    
    def _compute_micro_price(self, book: pl.DataFrame) -> float:
        """Compute micro price using multiple book levels."""
        bids = book.filter(pl.col("side") == "bid").sort("level")
        asks = book.filter(pl.col("side") == "ask").sort("level")
        
        if bids.is_empty() or asks.is_empty():
            return 0.0
        
        # Use top 5 levels
        n_levels = min(5, len(bids), len(asks))
        
        bid_value = 0.0
        bid_size = 0.0
        ask_value = 0.0
        ask_size = 0.0
        
        for i in range(n_levels):
            b_price = bids[i, "price"]
            b_size = bids[i, "size"]
            a_price = asks[i, "price"]
            a_size = asks[i, "size"]
            
            bid_value += b_price * b_size
            bid_size += b_size
            ask_value += a_price * a_size
            ask_size += a_size
        
        if bid_size + ask_size < 1e-8:
            return 0.0
        
        return (bid_value + ask_value) / (bid_size + ask_size)
    
    def _compute_depth_imbalance(self, book: pl.DataFrame) -> float:
        """Compute order book depth imbalance."""
        bid_depth = (
            book
            .filter(pl.col("side") == "bid")
            .select(pl.col("size").sum())[0, 0]
        )
        
        ask_depth = (
            book
            .filter(pl.col("side") == "ask")
            .select(pl.col("size").sum())[0, 0]
        )
        
        total_depth = bid_depth + ask_depth
        if total_depth < 1e-8:
            return 0.0
        
        return (bid_depth - ask_depth) / total_depth
    
    def _compute_effective_spread(self, trades: pl.DataFrame, mid_price: float) -> float:
        """Compute effective spread from recent trades."""
        if trades.is_empty() or mid_price <= 0:
            return 0.0
        
        # Last 10 trades
        recent_trades = trades.sort("timestamp_ns", descending=True).head(10)
        
        if recent_trades.is_empty():
            return 0.0
        
        # Compute average distance from mid
        spreads = recent_trades.with_columns(
            (2 * np.abs(pl.col("price") - mid_price) / mid_price * 10000).alias("spread_bps")
        )
        
        return spreads["spread_bps"].mean() or 0.0


# Numba-optimized functions for critical paths
@njit(parallel=True)
def compute_rolling_stats(values: np.ndarray, window: int) -> Tuple[np.ndarray, np.ndarray]:
    """Compute rolling mean and std using Numba."""
    n = len(values)
    means = np.zeros(n - window + 1)
    stds = np.zeros(n - window + 1)
    
    for i in prange(n - window + 1):
        window_data = values[i:i + window]
        means[i] = np.mean(window_data)
        stds[i] = np.std(window_data)
    
    return means, stds


@njit
def compute_ema(values: np.ndarray, alpha: float) -> np.ndarray:
    """Compute exponential moving average."""
    n = len(values)
    ema = np.zeros(n)
    ema[0] = values[0]
    
    for i in range(1, n):
        ema[i] = alpha * values[i] + (1 - alpha) * ema[i - 1]
    
    return ema