#!/usr/bin/env bash
set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${GREEN}🚀 BTC-USD Scalping Stack Bootstrap${NC}"
echo "======================================"

# Check OS
if [[ "$OSTYPE" == "linux-gnu"* ]]; then
    OS="linux"
elif [[ "$OSTYPE" == "darwin"* ]]; then
    OS="macos"
else
    echo -e "${RED}Unsupported OS: $OSTYPE${NC}"
    exit 1
fi

# Install Rust
if ! command -v cargo &> /dev/null; then
    echo -e "${YELLOW}Installing Rust...${NC}"
    curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh -s -- -y
    source "$HOME/.cargo/env"
fi

# Install Python 3.11
if ! command -v python3.11 &> /dev/null; then
    echo -e "${YELLOW}Installing Python 3.11...${NC}"
    if [[ "$OS" == "linux" ]]; then
        sudo apt update
        sudo apt install -y python3.11 python3.11-venv python3.11-dev
    else
        brew install python@3.11
    fi
fi

# Create virtual environment
echo -e "${YELLOW}Creating Python virtual environment...${NC}"
python3.11 -m venv venv
source venv/bin/activate
pip install --upgrade pip

# Install Python dependencies
echo -e "${YELLOW}Installing Python dependencies...${NC}"
cat > requirements.txt << 'EOF'
# Core
polars==0.20.0
numpy==1.24.3
pandas==2.0.3
pydantic==2.5.0
pydantic-settings==2.1.0

# ML
torch==2.1.0
scikit-learn==1.3.0
safetensors==0.4.0
mlflow==2.8.0

# Infrastructure
grpcio==1.59.0
grpcio-tools==1.59.0
protobuf==4.24.0
confluent-kafka==2.3.0
redis==5.0.0

# Testing
pytest==7.4.0
pytest-asyncio==0.21.0
hypothesis==6.90.0

# Monitoring
prometheus-client==0.18.0

# Dev tools
black==23.10.0
ruff==0.1.0
mypy==1.6.0
pre-commit==3.5.0
EOF

pip install -r requirements.txt

# Install Rust dependencies
echo -e "${YELLOW}Setting up Rust workspace...${NC}"
cat > Cargo.toml << 'EOF'
[workspace]
members = [
    "ingest-rs",
    "risk-rs",
]

[workspace.package]
version = "0.1.0"
edition = "2021"
authors = ["BTC Scalp Team"]

[workspace.dependencies]
tokio = { version = "1.34", features = ["full"] }
tokio-tungstenite = "0.21"
prost = "0.12"
tonic = "0.10"
rdkafka = { version = "0.35", features = ["cmake-build"] }
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
anyhow = "1.0"
tracing = "0.1"
tracing-subscriber = "0.3"
crossbeam = "0.8"
rust_decimal = "1.33"
EOF

# Create project structure
echo -e "${YELLOW}Creating project structure...${NC}"
mkdir -p {config,proto,ingest-rs/src,risk-rs/src,modellib,featurelib,backtest,deploy/{k8s,terraform},scripts,tests}

# Install Kafka/Redpanda
if ! command -v rpk &> /dev/null; then
    echo -e "${YELLOW}Installing Redpanda...${NC}"
    if [[ "$OS" == "linux" ]]; then
        curl -1sLf 'https://dl.redpanda.com/nzc4ZYQK3WRGd9sy/redpanda/cfg/setup/bash.deb.sh' | sudo -E bash
        sudo apt install -y redpanda
    else
        brew install redpanda-data/tap/redpanda
    fi
fi

# Install k3d for local Kubernetes
if ! command -v k3d &> /dev/null; then
    echo -e "${YELLOW}Installing k3d...${NC}"
    curl -s https://raw.githubusercontent.com/k3d-io/k3d/main/install.sh | bash
fi

# Install Tilt
if ! command -v tilt &> /dev/null; then
    echo -e "${YELLOW}Installing Tilt...${NC}"
    curl -fsSL https://raw.githubusercontent.com/tilt-dev/tilt/master/scripts/install.sh | bash
fi

# Install Protocol Buffers compiler
if ! command -v protoc &> /dev/null; then
    echo -e "${YELLOW}Installing protoc...${NC}"
    if [[ "$OS" == "linux" ]]; then
        sudo apt install -y protobuf-compiler
    else
        brew install protobuf
    fi
fi

# Generate initial configs
echo -e "${YELLOW}Generating configuration files...${NC}"
cat > config/default.toml << 'EOF'
[venue.binance]
ws_url = "wss://stream.binance.com:9443/ws"
api_key = {env = "BINANCE_API_KEY"}
api_secret = {env = "BINANCE_SECRET"}

[strategy.scalp]
lookback_seconds = 300
prediction_horizon = 300
kelly_cap = 0.10
max_leverage = 2.0
max_dd_pct = 1.0
target_vol_5m = 0.005

[features]
window_ms = [5_000, 30_000, 60_000, 300_000]
book_levels = 10
trade_imbalance_depth = 50
sentiment_zscore_half_life = 30

[ml.model]
type = "TemporalCNN"
quantization_bits = 8
calibration_method = "Platt"

[infrastructure.kafka]
bootstrap_servers = "localhost:9092"
topics = ["ticks.raw", "features.micro", "signals", "orders"]

[monitoring]
prometheus_port = 9090
latency_threshold_ms = 5
EOF

# Setup pre-commit hooks
echo -e "${YELLOW}Setting up pre-commit hooks...${NC}"
cat > .pre-commit-config.yaml << 'EOF'
repos:
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.4.0
    hooks:
      - id: trailing-whitespace
      - id: end-of-file-fixer
      - id: check-yaml
      - id: check-added-large-files
  
  - repo: https://github.com/psf/black
    rev: 23.10.0
    hooks:
      - id: black
  
  - repo: https://github.com/charliermarsh/ruff-pre-commit
    rev: v0.1.0
    hooks:
      - id: ruff
EOF

pre-commit install

# Create Makefile
echo -e "${YELLOW}Creating Makefile...${NC}"
cat > Makefile << 'EOF'
.PHONY: all build test bench run clean

all: build test

build:
	@echo "Building Rust components..."
	cargo build --release
	@echo "Building Python packages..."
	python -m build

test:
	@echo "Running Rust tests..."
	cargo test
	@echo "Running Python tests..."
	pytest -v

bench:
	@echo "Running latency benchmarks..."
	cargo bench
	python scripts/benchmark_latency.py

run:
	@echo "Starting development stack..."
	tilt up

clean:
	cargo clean
	rm -rf build/ dist/ *.egg-info
	find . -type d -name __pycache__ -exec rm -rf {} +

proto:
	@echo "Generating protobuf code..."
	protoc --python_out=. --pyi_out=. proto/*.proto
	cargo build --features proto
EOF

echo -e "${GREEN}✅ Bootstrap complete!${NC}"
echo -e "${YELLOW}Next steps:${NC}"
echo "1. Export your API keys:"
echo "   export BINANCE_API_KEY='your-key'"
echo "   export BINANCE_SECRET='your-secret'"
echo "2. Start Redpanda: rpk redpanda start"
echo "3. Run tests: make test"
echo "4. Start development: tilt up"