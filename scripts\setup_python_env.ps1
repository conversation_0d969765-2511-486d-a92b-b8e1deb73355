# PowerShell script to setup Python environment
Write-Host "Setting up Python environment..." -ForegroundColor Green

# Add Python to current session PATH
$pythonPath = "$env:LOCALAPPDATA\Programs\Python\Python311"
$scriptsPath = "$env:LOCALAPPDATA\Programs\Python\Python311\Scripts"

if (Test-Path $pythonPath) {
    $env:PATH = "$pythonPath;$scriptsPath;$env:PATH"
    Write-Host "Python added to current session PATH" -ForegroundColor Green
    
    # Test Python
    Write-Host "Testing Python installation..." -ForegroundColor Yellow
    & "$pythonPath\python.exe" --version
    
    # Test pip
    Write-Host "Testing pip..." -ForegroundColor Yellow
    & "$pythonPath\python.exe" -m pip --version
    
    Write-Host "Python is ready to use in this session!" -ForegroundColor Green
    Write-Host "You can now run: python scripts\generate_remaining_files.py" -ForegroundColor Cyan
} else {
    Write-Host "Python installation not found at expected location" -ForegroundColor Red
    Write-Host "Expected: $pythonPath" -ForegroundColor Red
}
