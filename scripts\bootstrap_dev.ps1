# PowerShell Bootstrap Script for BTC-USD Scalping Stack
param(
    [switch]$SkipRust,
    [switch]$SkipPython,
    [switch]$SkipGit
)

Write-Host "🚀 BTC-USD Scalping Stack Bootstrap" -ForegroundColor Green
Write-Host "======================================" -ForegroundColor Green

# Set Python path for current session
$pythonPath = "$env:LOCALAPPDATA\Programs\Python\Python311"
$scriptsPath = "$env:LOCALAPPDATA\Programs\Python\Python311\Scripts"
if (Test-Path $pythonPath) {
    $env:PATH = "$pythonPath;$scriptsPath;$env:PATH"
    Write-Host "✅ Python added to current session PATH" -ForegroundColor Green
}

# Check if Python is available
if (-not $SkipPython) {
    Write-Host "🐍 Checking Python installation..." -ForegroundColor Yellow
    try {
        $pythonVersion = & python --version 2>&1
        Write-Host "✅ Found: $pythonVersion" -ForegroundColor Green
    }
    catch {
        Write-Host "❌ Python not found. Please install Python 3.11+" -ForegroundColor Red
        Write-Host "Run: winget install Python.Python.3.11" -ForegroundColor Cyan
        exit 1
    }
}

# Create virtual environment
if (-not $SkipPython) {
    Write-Host "🔧 Creating Python virtual environment..." -ForegroundColor Yellow
    if (-not (Test-Path "venv")) {
        python -m venv venv
        Write-Host "✅ Virtual environment created" -ForegroundColor Green
    } else {
        Write-Host "✅ Virtual environment already exists" -ForegroundColor Green
    }
    
    # Activate virtual environment
    Write-Host "🔧 Activating virtual environment..." -ForegroundColor Yellow
    & "venv\Scripts\Activate.ps1"
    
    # Upgrade pip
    Write-Host "🔧 Upgrading pip..." -ForegroundColor Yellow
    python -m pip install --upgrade pip
}

# Install Python dependencies
if (-not $SkipPython) {
    Write-Host "📦 Installing Python dependencies..." -ForegroundColor Yellow
    
    # Create requirements.txt if it doesn't exist
    if (-not (Test-Path "requirements.txt")) {
        @"
# Core
polars==0.20.0
numpy==1.24.3
pandas==2.0.3
pydantic==2.5.0
pydantic-settings==2.1.0

# ML
torch==2.1.0
scikit-learn==1.3.0
safetensors==0.4.0
mlflow==2.8.0

# Infrastructure
grpcio==1.59.0
grpcio-tools==1.59.0
protobuf==4.24.0
confluent-kafka==2.3.0
redis==5.0.0

# Testing
pytest==7.4.0
pytest-asyncio==0.21.0
hypothesis==6.90.0

# Monitoring
prometheus-client==0.18.0

# Dev tools
black==23.10.0
ruff==0.1.0
mypy==1.6.0
pre-commit==3.5.0
"@ | Out-File -FilePath "requirements.txt" -Encoding UTF8
    }
    
    pip install -r requirements.txt
    Write-Host "✅ Python dependencies installed" -ForegroundColor Green
}

# Check Rust installation
if (-not $SkipRust) {
    Write-Host "🦀 Checking Rust installation..." -ForegroundColor Yellow
    try {
        $rustVersion = & cargo --version 2>&1
        Write-Host "✅ Found: $rustVersion" -ForegroundColor Green
    }
    catch {
        Write-Host "❌ Rust not found. Installing Rust..." -ForegroundColor Yellow
        Write-Host "Please install Rust from: https://rustup.rs/" -ForegroundColor Cyan
        Write-Host "Or run: winget install Rustlang.Rustup" -ForegroundColor Cyan
    }
}

# Create project structure
Write-Host "📁 Creating project structure..." -ForegroundColor Yellow
$directories = @(
    "config", "proto", "ingest-rs\src", "risk-rs\src", 
    "modellib", "featurelib", "backtest", 
    "deploy\k8s", "deploy\terraform", "scripts", "tests"
)

foreach ($dir in $directories) {
    if (-not (Test-Path $dir)) {
        New-Item -ItemType Directory -Path $dir -Force | Out-Null
        Write-Host "  Created: $dir" -ForegroundColor Gray
    }
}
Write-Host "✅ Project structure created" -ForegroundColor Green

# Create workspace Cargo.toml if it doesn't exist
if (-not (Test-Path "Cargo.toml")) {
    Write-Host "🦀 Creating Rust workspace..." -ForegroundColor Yellow
    @"
[workspace]
members = [
    "ingest-rs",
    "risk-rs",
]

[workspace.package]
version = "0.1.0"
edition = "2021"
authors = ["BTC Scalp Team"]

[workspace.dependencies]
tokio = { version = "1.34", features = ["full"] }
tokio-tungstenite = "0.21"
prost = "0.12"
tonic = "0.10"
rdkafka = { version = "0.35", features = ["cmake-build"] }
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
anyhow = "1.0"
tracing = "0.1"
tracing-subscriber = "0.3"
crossbeam = "0.8"
rust_decimal = "1.33"
"@ | Out-File -FilePath "Cargo.toml" -Encoding UTF8
    Write-Host "✅ Rust workspace created" -ForegroundColor Green
}

Write-Host "✅ Bootstrap complete!" -ForegroundColor Green
Write-Host ""
Write-Host "Next steps:" -ForegroundColor Yellow
Write-Host "1. Configure your .env file with API keys" -ForegroundColor Cyan
Write-Host "2. Run: make test" -ForegroundColor Cyan
Write-Host "3. Start development with: tilt up" -ForegroundColor Cyan
