[workspace]
members = [
    "ingest-rs",
    "risk-rs",
]

[workspace.package]
version = "0.1.0"
edition = "2021"
authors = ["BTC Scalp Team"]

[workspace.dependencies]
tokio = { version = "1.34", features = ["full"] }
tokio-tungstenite = "0.21"
prost = "0.12"
tonic = "0.10"
rdkafka = { version = "0.35", features = ["cmake-build"] }
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
anyhow = "1.0"
tracing = "0.1"
tracing-subscriber = "0.3"
crossbeam = "0.8"
rust_decimal = "1.33"