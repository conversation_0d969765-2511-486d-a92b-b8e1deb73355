[package]
name = "risk-rs"
version = "0.1.0"
edition = "2021"

[dependencies]
anyhow = "1.0"
rust_decimal = "1.33"
rust_decimal_macros = "1.33"
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
tracing = "0.1"
tracing-subscriber = { version = "0.3", features = ["env-filter"] }
prost = "0.12"
tonic = "0.10"
uuid = { version = "1.6", features = ["v4", "serde"] }
tokio = { version = "1.34", features = ["full"] }
crossbeam = "0.8"

[build-dependencies]
prost-build = "0.12"
tonic-build = "0.10"

[[bin]]
name = "risk-engine"
path = "src/main.rs"

[lib]
name = "risk"
path = "src/lib.rs"

[profile.release]
lto = true
codegen-units = 1
opt-level = 3