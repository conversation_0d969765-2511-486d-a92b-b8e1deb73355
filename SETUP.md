# BTC Scalping System - Setup Instructions

## Prerequisites

Before starting, ensure you have the following installed:

- **Python 3.11+**
- **Rust 1.74+** (with cargo)
- **Docker & Docker Compose**
- **kubectl** (for Kubernetes deployment)
- **Git**

## Quick Start

### 1. <PERSON>lone and Setup

```bash
# Clone the repository (when available on GitHub)
git clone https://github.com/your-org/btc-scalp.git
cd btc-scalp

# Run the bootstrap script
chmod +x scripts/bootstrap_dev.sh
./scripts/bootstrap_dev.sh
```

### 2. Configure Environment

```bash
# Copy environment template
cp .env.example .env

# Edit .env with your Binance credentials
# IMPORTANT: Never commit .env to version control!
nano .env
```

### 3. Verify Setup

```bash
# Run verification script
python scripts/verify_setup.py
```

### 4. Start Development Environment

```bash
# Using Tilt (recommended)
tilt up

# Or using Docker Compose
docker-compose -f docker-compose.test.yml up -d
```

## Development Workflow

See README.md for detailed development instructions.
