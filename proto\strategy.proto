syntax = "proto3";

package strategy.v1;

import "google/protobuf/timestamp.proto";

// Market data messages
message TradeTick {
  google.protobuf.Timestamp timestamp = 1;
  int64 timestamp_ns = 2;
  double price = 3;
  double size = 4;
  bool is_buy = 5;
  string exchange = 6;
  string trade_id = 7;
}

message OrderBookL2 {
  google.protobuf.Timestamp timestamp = 1;
  int64 timestamp_ns = 2;
  repeated PriceLevel bids = 3;
  repeated PriceLevel asks = 4;
  string exchange = 5;
  int64 sequence_number = 6;
}

message PriceLevel {
  double price = 1;
  double size = 2;
  int32 num_orders = 3;
}

message FundingUpdate {
  google.protobuf.Timestamp timestamp = 1;
  double funding_rate = 2;
  double mark_price = 3;
  double index_price = 4;
  int64 next_funding_time = 5;
}

// Feature messages
message MicroFeatureVector {
  int64 timestamp_ns = 1;
  repeated double values = 2;
  map<string, double> named_features = 3;
  repeated WindowedFeature windowed_features = 4;
}

message WindowedFeature {
  string name = 1;
  int32 window_ms = 2;
  double value = 3;
}

// Trading signals
message Signal {
  int64 timestamp_ns = 1;
  double p_up = 2;
  double confidence = 3;
  double expected_edge_bps = 4;
  Prediction prediction = 5;
  string model_version = 6;
}

message Prediction {
  double p_up = 1;
  double q_low = 2;  // 5th percentile
  double q_high = 3; // 95th percentile
  map<string, double> metadata = 4;
}

// Risk and order management
message RiskOrder {
  string order_id = 1;
  int64 timestamp_ns = 2;
  OrderSide side = 3;
  double size = 4;
  double limit_price = 5;
  TimeInForce tif = 6;
  double stop_price = 7;
  RiskLimits risk_limits = 8;
}

message RiskLimits {
  double max_position_size = 1;
  double max_leverage = 2;
  double stop_loss_pct = 3;
  double daily_loss_limit = 4;
}

message Fill {
  string order_id = 1;
  string fill_id = 2;
  int64 timestamp_ns = 3;
  double price = 4;
  double size = 5;
  double fee = 6;
  string fee_currency = 7;
}

// Enums
enum OrderSide {
  ORDER_SIDE_UNSPECIFIED = 0;
  ORDER_SIDE_BUY = 1;
  ORDER_SIDE_SELL = 2;
}

enum TimeInForce {
  TIME_IN_FORCE_UNSPECIFIED = 0;
  TIME_IN_FORCE_IOC = 1;
  TIME_IN_FORCE_GTC = 2;
  TIME_IN_FORCE_FOK = 3;
}

// Service definitions
service TradingStrategy {
  rpc StreamSignals(StreamSignalsRequest) returns (stream Signal);
  rpc SubmitOrder(RiskOrder) returns (OrderResponse);
  rpc GetPosition(GetPositionRequest) returns (PositionSnapshot);
}

message StreamSignalsRequest {
  string symbol = 1;
  repeated string feature_names = 2;
}

message OrderResponse {
  string order_id = 1;
  bool accepted = 2;
  string rejection_reason = 3;
}

message GetPositionRequest {
  string symbol = 1;
}

message PositionSnapshot {
  double size = 1;
  double avg_entry_price = 2;
  double unrealized_pnl = 3;
  double realized_pnl = 4;
  int64 timestamp_ns = 5;
}