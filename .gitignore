# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
venv/
env/
ENV/
.venv
pip-log.txt
pip-delete-this-directory.txt
.pytest_cache/
.coverage
htmlcov/
.tox/
.mypy_cache/
.dmypy.json
dmypy.json
*.egg-info/
dist/
build/
*.egg

# Rust
target/
Cargo.lock
**/*.rs.bk
*.pdb

# IDEs
.idea/
.vscode/
*.swp
*.swo
*~
.DS_Store

# Project specific
data/
models/
logs/
*.log
latency_benchmark_*.json
backtest_results.png
config/prod.toml
config/staging.toml
.env
.env.*

# Kubernetes
kubeconfig
*.key
*.crt
*.pem

# Docker
.dockerignore

# Terraform
*.tfstate
*.tfstate.*
.terraform/
.terraform.lock.hcl

# Test artifacts
.tox/
.coverage
coverage.xml
*.cover
.hypothesis/
test-results/

# ML artifacts
mlruns/
*.safetensors
*.pkl
*.npy
*.h5

# Jupyter
.ipynb_checkpoints/
*.ipynb

# OS
.DS_Store
Thumbs.db

# Secrets
secrets/
*.secret
credentials.json